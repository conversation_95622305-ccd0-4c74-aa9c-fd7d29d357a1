import type { Plugin, PluginRenderPageLayer } from '@react-pdf-viewer/core';
import { Check, Copy, Info, Languages, MessageCircleQuestion, Search } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import type { OCRHighlight } from './document-viewer';

interface HighlightPluginProps {
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
  selectedHighlightChunkId?: number | null;
}

interface HighlightOverlayProps {
  highlights: OCRHighlight[];
  pageIndex: number;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
  selectedHighlightChunkId?: number | null;
}

// HoverToolbar interface removed - no action bar menu needed

// HoverToolbar component removed - no action bar menu needed



const HighlightOverlay: React.FC<HighlightOverlayProps> = ({
  highlights,
  pageIndex,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
  selectedHighlightChunkId,
}) => {
  const pageHighlights = highlights.filter((h) => h.pageIndex === pageIndex);
  const [hoveredHighlightId, setHoveredHighlightId] = useState<string | null>(null);
  const hideTimerRef = useRef<number | null>(null);

  // Debug logging
  console.log(`🟡 HighlightOverlay page ${pageIndex}:`, {
    totalHighlights: highlights.length,
    pageHighlights: pageHighlights.length,
    pageHighlightIds: pageHighlights.map(h => h.id),
  });

  if (pageHighlights.length === 0) {
    return null;
  }

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getHighlightStyle = (highlight: OCRHighlight, isHovered: boolean) => {
    const { boundingBox, confidence, layoutType } = highlight;

    // Convert normalized coordinates to percentages
    const left = `${boundingBox.left * 100}%`;
    const top = `${boundingBox.top * 100}%`;
    const width = `${boundingBox.width * 100}%`;
    const height = `${boundingBox.height * 100}%`;

    // Check if this highlight is the selected one (based on chunkId)
    const isSelectedHighlight =
      selectedHighlightChunkId &&
      highlight.chunkId &&
      (typeof highlight.chunkId === 'string'
        ? Number.parseInt(highlight.chunkId) === selectedHighlightChunkId
        : highlight.chunkId === selectedHighlightChunkId);

    // Only log for selected highlights to reduce noise
    if (isSelectedHighlight) {
      console.log('🟡 SELECTED HIGHLIGHT FOUND on page:', {
        highlightId: highlight.id,
        highlightChunkId: highlight.chunkId,
        selectedHighlightChunkId,
        isSelectedHighlight,
        pageIndex: pageIndex,
        highlightPageIndex: highlight.pageIndex,
        text: `${highlight.text.substring(0, 50)}...`,
      });
    }

    // Color based on confidence - show when hovered OR when it's the selected highlight
    let backgroundColor: string;
    let borderColor: string;

    if (isHovered || isSelectedHighlight) {
      if (isSelectedHighlight) {
        // Special styling for selected highlights - brighter and more prominent
        backgroundColor = 'rgba(59, 130, 246, 0.2)'; // Blue
        borderColor = 'rgba(59, 130, 246, 0.8)';
      } else if (confidence >= 0.9) {
        backgroundColor = 'rgba(34, 197, 94, 0.1)'; // Green
        borderColor = 'rgba(34, 197, 94, 0.5)';
      } else if (confidence >= 0.7) {
        backgroundColor = 'rgba(234, 179, 8, 0.1)'; // Yellow
        borderColor = 'rgba(234, 179, 8, 0.5)';
      } else {
        backgroundColor = 'rgba(239, 68, 68, 0.1)'; // Red
        borderColor = 'rgba(239, 68, 68, 0.5)';
      }

      // Layout type specific adjustments (only for non-selected highlights)
      if (!isSelectedHighlight && (layoutType === 'TITLE' || layoutType === 'LAYOUT_TITLE')) {
        backgroundColor = 'rgba(147, 51, 234, 0.1)'; // Purple for titles
        borderColor = '#7c3aed';
      }
    } else {
      // Transparent when not hovered or selected
      backgroundColor = 'transparent';
      borderColor = 'transparent';
    }

    // Slightly expand the box (1px each side) so the 1px border doesn't overlap text
    const expandedLeft = `calc(${left} - 2px)`;
    const expandedTop = `calc(${top} - 2px)`;
    const expandedWidth = `calc(${width} + 4px)`;
    const expandedHeight = `calc(${height} + 4px)`;

    return {
      position: 'absolute' as const,
      left: expandedLeft,
      top: expandedTop,
      width: expandedWidth,
      height: expandedHeight,
      backgroundColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '2px',
      cursor: enableInteractions ? 'pointer' : 'default',
      pointerEvents: (enableInteractions ? 'auto' : 'none') as React.CSSProperties['pointerEvents'],
      transition: 'all 0.2s ease-in-out',
      zIndex: 10,
    };
  };

  const keepToolbarVisible = () => {
    if (hideTimerRef.current) {
      window.clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
  };

  const scheduleHide = () => {
    if (hideTimerRef.current) {
      window.clearTimeout(hideTimerRef.current);
    }
    hideTimerRef.current = window.setTimeout(() => {
      setHoveredHighlightId(null);
      onHighlightHover?.(null);
      hideTimerRef.current = null;
    }, 200);
  };

  return (
    <div className="pointer-events-none absolute inset-0" style={{ zIndex: 1 }}>
      {pageHighlights.map((highlight) => {
        const isHovered = hoveredHighlightId === highlight.id;
        const isSelectedHighlight =
          selectedHighlightChunkId &&
          highlight.chunkId &&
          (typeof highlight.chunkId === 'string'
            ? Number.parseInt(highlight.chunkId) === selectedHighlightChunkId
            : highlight.chunkId === selectedHighlightChunkId);

        // Log each highlight check for this page (only when we have a selected highlight)
        if (selectedHighlightChunkId && pageHighlights.length > 0) {
          console.log('🟡 Page', pageIndex, 'highlight check:', {
            highlightId: highlight.id,
            highlightChunkId: highlight.chunkId,
            selectedHighlightChunkId,
            isSelectedHighlight,
            text: `${highlight.text.substring(0, 30)}...`,
          });
        }

        const highlightStyle = getHighlightStyle(highlight, isHovered);

        return (
          <React.Fragment key={highlight.id}>
            {/* Highlight Area */}
            <div
              data-highlight-id={highlight.id}
              data-chunk-id={highlight.chunkId}
              style={highlightStyle}
              onClick={() => enableInteractions && onHighlightClick?.(highlight)}
              onMouseEnter={() => {
                if (enableInteractions) {
                  setHoveredHighlightId(highlight.id);
                  onHighlightHover?.(highlight);
                  keepToolbarVisible();
                }
              }}
              onMouseLeave={() => {
                if (enableInteractions) {
                  scheduleHide();
                }
              }}
            />

            {/* No hover toolbar - removed as requested */}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export const createHighlightPlugin = ({
  highlights,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
  selectedHighlightChunkId,
}: HighlightPluginProps): Plugin => {
  console.log('🟠 Creating highlight plugin with:', {
    highlightsCount: highlights.length,
    selectedHighlightChunkId,
    enableInteractions,
  });

  const renderPageLayer = (props: PluginRenderPageLayer) => {
    console.log(`🟠 Rendering page layer for page ${props.pageIndex}:`, {
      width: props.width,
      height: props.height,
      highlightsCount: highlights.length,
    });

    return (
      <div
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: props.width,
          height: props.height,
          zIndex: 1, // Lower z-index to not interfere with text selection
          pointerEvents: 'none', // Container is non-interactive
        }}
      >
        <HighlightOverlay
          highlights={highlights}
          pageIndex={props.pageIndex}
          onHighlightClick={onHighlightClick}
          onHighlightHover={onHighlightHover}
          enableInteractions={enableInteractions}
          selectedHighlightChunkId={selectedHighlightChunkId}
        />
      </div>
    );
  };

  return { renderPageLayer };
};
